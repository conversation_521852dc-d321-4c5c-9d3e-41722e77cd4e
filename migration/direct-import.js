#!/usr/bin/env node

/**
 * Direct import script for IsotopeAI data to Supabase
 * Use this if the web migration page has issues
 */

import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration - UPDATE THESE VALUES
const SUPABASE_URL = 'https://pcfrgvhigvklersufktf.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjZnJndmhpZ3ZrbGVyc3Vma3RmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc2MDkzNywiZXhwIjoyMDY0MzM2OTM3fQ.fKrM48gLm71Q7F6ioePKmX6xacrC6o-nasdmNJ6CULc'; // Get from Supabase dashboard

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

console.log('🚀 IsotopeAI Direct Import to Supabase');
console.log('=====================================\n');

// Parse CSV function
function parseCSV(csvText) {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
        const values = [];
        let current = '';
        let inQuotes = false;

        for (let j = 0; j < lines[i].length; j++) {
            const char = lines[i][j];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        values.push(current.trim());

        if (values.length === headers.length) {
            const row = {};
            headers.forEach((header, index) => {
                let value = values[index]?.replace(/^"|"$/g, '').replace(/""/g, '"') || '';
                
                // Parse JSON fields
                if (header.includes('messages') || header.includes('members') || 
                    header.includes('stats') || header.includes('progress') ||
                    header.includes('subject_marks')) {
                    try {
                        value = value ? JSON.parse(value) : null;
                    } catch (e) {
                        console.warn(`Failed to parse JSON for ${header}:`, value);
                        value = null;
                    }
                }
                
                // Parse boolean fields
                if (header.includes('is_') || header === 'completed') {
                    value = value === 'true' || value === '1';
                }
                
                // Parse numeric fields
                if (header.includes('count') || header.includes('marks') || 
                    header === 'duration') {
                    value = value ? parseInt(value) || 0 : 0;
                }

                row[header] = value;
            });
            data.push(row);
        }
    }

    return data;
}

// Import functions
async function importUsers(csvFile) {
    console.log('👥 Importing users...');
    
    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const users = parseCSV(csvText);
    
    console.log(`📊 Found ${users.length} users to import`);
    
    let imported = 0;
    let errors = 0;
    
    for (const user of users) {
        try {
            const userData = {
                id: user.id,
                email: user.email,
                display_name: user.display_name,
                photo_url: user.photo_url,
                username: user.username,
                created_at: user.created_at || new Date().toISOString(),
                updated_at: new Date().toISOString(),
                last_login: user.last_login || new Date().toISOString(),
                stats: user.stats || {},
                progress: user.progress || {}
            };

            const { error } = await supabase
                .from('users')
                .upsert(userData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing user ${user.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${users.length} users`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing user ${user.id}:`, err.message);
            errors++;
        }
    }
    
    console.log(`✅ Users import complete: ${imported} imported, ${errors} errors\n`);
}

async function importGroups(csvFile) {
    console.log('👥 Importing groups...');
    
    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const groups = parseCSV(csvText);
    
    console.log(`📊 Found ${groups.length} groups to import`);
    
    let importedGroups = 0;
    let importedMessages = 0;
    let errors = 0;
    
    for (const group of groups) {
        try {
            // Import group
            const groupData = {
                id: group.id,
                name: group.name,
                description: group.description || null,
                members: group.members || [],
                created_by: group.created_by,
                created_at: group.created_at || new Date().toISOString(),
                updated_at: new Date().toISOString(),
                is_public: group.is_public || false,
                invite_code: group.invite_code || null,
                owner_id: group.created_by
            };

            const { error: groupError } = await supabase
                .from('groups')
                .upsert(groupData, { onConflict: 'id' });

            if (groupError) {
                console.error(`❌ Error importing group ${group.id}:`, groupError.message);
                errors++;
                continue;
            }

            importedGroups++;

            // Import messages if they exist
            if (group.messages && Array.isArray(group.messages)) {
                for (const message of group.messages) {
                    try {
                        const messageData = {
                            id: message.id,
                            group_id: group.id,
                            sender_id: message.senderId,
                            content: message.content,
                            created_at: message.timestamp || new Date().toISOString(),
                            updated_at: message.timestamp || new Date().toISOString()
                        };

                        const { error: messageError } = await supabase
                            .from('messages')
                            .upsert(messageData, { onConflict: 'id' });

                        if (!messageError) {
                            importedMessages++;
                        }
                    } catch (msgErr) {
                        console.warn(`⚠️  Error importing message in group ${group.id}:`, msgErr.message);
                    }
                }
            }

            if (importedGroups % 5 === 0) {
                console.log(`✅ Imported ${importedGroups}/${groups.length} groups, ${importedMessages} messages`);
            }
        } catch (err) {
            console.error(`❌ Exception importing group ${group.id}:`, err.message);
            errors++;
        }
    }
    
    console.log(`✅ Groups import complete: ${importedGroups} groups, ${importedMessages} messages, ${errors} errors\n`);
}

async function importAIChats(csvFile) {
    console.log('🤖 Importing AI chats...');
    
    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const chats = parseCSV(csvText);
    
    console.log(`📊 Found ${chats.length} AI chats to import`);
    
    let imported = 0;
    let errors = 0;
    
    for (const chat of chats) {
        try {
            const chatData = {
                id: chat.id,
                user_id: chat.user_id,
                title: chat.title || null,
                messages: chat.messages || [],
                created_at: chat.created_at || new Date().toISOString(),
                updated_at: chat.updated_at || new Date().toISOString(),
                is_public: chat.is_public || false,
                view_count: chat.view_count || 0,
                created_by: chat.user_id,
                slug: chat.slug || null,
                preview: chat.preview || null,
                status: chat.status || 'approved',
                tags: chat.tags || [],
                is_pinned: chat.is_pinned || false,
                is_starred: chat.is_starred || false
            };

            const { error } = await supabase
                .from('ai_chats')
                .upsert(chatData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing AI chat ${chat.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${chats.length} AI chats`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing AI chat ${chat.id}:`, err.message);
            errors++;
        }
    }
    
    console.log(`✅ AI chats import complete: ${imported} imported, ${errors} errors\n`);
}

async function importExams(csvFile) {
    console.log('📝 Importing exams...');
    
    if (!fs.existsSync(csvFile)) {
        console.log(`❌ File not found: ${csvFile}`);
        return;
    }

    const csvText = fs.readFileSync(csvFile, 'utf8');
    const exams = parseCSV(csvText);
    
    console.log(`📊 Found ${exams.length} exams to import`);
    
    let imported = 0;
    let errors = 0;
    
    for (const exam of exams) {
        try {
            const examData = {
                id: exam.id,
                user_id: exam.user_id,
                name: exam.name,
                date: exam.date, // Assuming date is already in a valid format (e.g., YYYY-MM-DD)
                subject_marks: exam.subject_marks || [],
                total_marks_obtained: exam.total_marks_obtained || 0,
                total_marks: exam.total_marks || 0,
                notes: exam.notes || null,
                created_at: exam.created_at || new Date().toISOString(),
                updated_at: exam.updated_at || new Date().toISOString()
            };

            const { error } = await supabase
                .from('exams')
                .upsert(examData, { onConflict: 'id' });

            if (error) {
                console.error(`❌ Error importing exam ${exam.id}:`, error.message);
                errors++;
            } else {
                imported++;
                if (imported % 10 === 0) {
                    console.log(`✅ Imported ${imported}/${exams.length} exams`);
                }
            }
        } catch (err) {
            console.error(`❌ Exception importing exam ${exam.id}:`, err.message);
            errors++;
        }
    }
    
    console.log(`✅ Exams import complete: ${imported} imported, ${errors} errors\n`);
}

// Main import function
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node direct-import.js <csv-file> [csv-file2] [csv-file3]...');
        console.log('');
        console.log('Examples:');
        console.log('  node direct-import.js isotope-all-users.csv');
        console.log('  node direct-import.js isotope-all-groups.csv');
        console.log('  node direct-import.js isotope-all-ai-chats.csv');
        console.log('  node direct-import.js isotope-exams.csv');
        console.log('  node direct-import.js *.csv  # Import all CSV files');
        console.log('');
        console.log('Make sure to update SUPABASE_SERVICE_KEY in this script first!');
        return;
    }

    if (SUPABASE_SERVICE_KEY === 'YOUR_SUPABASE_SERVICE_KEY') {
        console.log('❌ Please update SUPABASE_SERVICE_KEY in this script first!');
        console.log('   Get it from: Supabase Dashboard → Settings → API → service_role key');
        return;
    }

    for (const csvFile of args) {
        console.log(`📁 Processing: ${csvFile}`);
        
        if (csvFile.includes('users')) {
            await importUsers(csvFile);
        } else if (csvFile.includes('groups')) {
            await importGroups(csvFile);
        } else if (csvFile.includes('ai-chats') || csvFile.includes('chats')) {
            await importAIChats(csvFile);
        } else if (csvFile.includes('exams')) {
            await importExams(csvFile);
        } else {
            console.log(`⚠️  Unknown file type: ${csvFile} - skipping`);
        }
    }
    
    console.log('🎉 Import complete!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Verify data in Supabase dashboard');
    console.log('2. Test the application with imported data');
    console.log('3. Begin code migration phase');
}

main().catch(console.error);
