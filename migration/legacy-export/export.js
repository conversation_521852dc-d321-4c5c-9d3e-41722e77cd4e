// Firebase configuration - You need to replace these with your actual Firebase config values
// Get these from your Firebase project settings
const firebaseConfig = {
    apiKey: "AIzaSyA2odde622bEF93tx3KcwBymHVUMdvXnjk", // Replace with your actual API key
    authDomain: "doubtgpt.firebaseapp.com", // Replace with your actual auth domain
    projectId: "doubtgpt", // Replace with your actual project ID
    storageBucket: "doubtgpt.firebasestorage.app", // Replace with your actual storage bucket
    messagingSenderId: "120215440192", // Replace with your actual sender ID
    appId: "1:120215440192:web:aeb308ad5df2b659bd48a5" // Replace with your actual app ID
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

let currentUser = null;
const ADMIN_EMAIL = '<EMAIL>';

// Check if current user is admin
function isAdmin() {
    return currentUser && currentUser.email === ADMIN_EMAIL;
}

// Authentication
document.getElementById('signInBtn').addEventListener('click', signIn);

async function signIn() {
    try {
        const provider = new firebase.auth.GoogleAuthProvider();
        provider.addScope('https://www.googleapis.com/auth/userinfo.email');
        provider.addScope('https://www.googleapis.com/auth/userinfo.profile');
        
        const result = await auth.signInWithPopup(provider);
        currentUser = result.user;
        
        showAuthStatus('Successfully signed in!', 'success');
        showExportSection();
    } catch (error) {
        console.error('Sign in error:', error);
        showAuthStatus('Sign in failed: ' + error.message, 'error');
    }
}

function showAuthStatus(message, type) {
    const statusEl = document.getElementById('authStatus');
    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = 'block';
}

function showExportSection() {
    document.getElementById('authSection').style.display = 'none';
    document.getElementById('exportSection').classList.add('active');

    // Show user info
    const userInfoEl = document.getElementById('userInfo');
    const adminBadge = isAdmin() ? '<span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin-left: 10px;">ADMIN</span>' : '';
    userInfoEl.innerHTML = `
        <strong>Signed in as:</strong> ${currentUser.displayName || currentUser.email}${adminBadge}<br>
        <strong>Email:</strong> ${currentUser.email}<br>
        <strong>User ID:</strong> ${currentUser.uid}
        ${isAdmin() ? '<br><strong>Admin Mode:</strong> Can export all user data' : ''}
    `;

    // Show admin export options if user is admin
    if (isAdmin()) {
        showAdminExportOptions();
    }
}

function showAdminExportOptions() {
    const exportSection = document.getElementById('exportSection');

    // Add admin export section
    const adminSection = document.createElement('div');
    adminSection.innerHTML = `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <h4 style="color: #856404; margin-bottom: 10px;">🔧 Admin Export Options</h4>
            <p style="color: #856404; margin-bottom: 15px;">As an admin, you can export data from all users:</p>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Users Data</h3>
                    <p>Export all user profiles and account information</p>
                    <div id="allUsersProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allUsersStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allUsers')">Export All Users</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All AI Chats</h3>
                    <p>Export all AI conversations from all users</p>
                    <div id="allAIChatsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allAIChatsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allAIChats')">Export All Chats</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Groups</h3>
                    <p>Export all groups and messages from all users</p>
                    <div id="allGroupsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allGroupsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allGroups')">Export All Groups</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Todos</h3>
                    <p>Export all todos and tasks from all users</p>
                    <div id="allTodosProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allTodosStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allTodos')">Export All Todos</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All User Subjects</h3>
                    <p>Export all custom subjects from all users</p>
                    <div id="allSubjectsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allSubjectsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allSubjects')">Export All Subjects</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Exams</h3>
                    <p>Export all exam countdowns from all users</p>
                    <div id="allExamsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allExamsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allExams')">Export All Exams</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Study Sessions</h3>
                    <p>Export all study sessions from all users</p>
                    <div id="allStudySessionsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allStudySessionsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allStudySessions')">Export All Study Sessions</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Mock Tests</h3>
                    <p>Export all mock tests from all users</p>
                    <div id="allMockTestsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allMockTestsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allMockTests')">Export All Mock Tests</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Chat Comments</h3>
                    <p>Export all comments from AI chats</p>
                    <div id="allChatCommentsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allChatCommentsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allChatComments')">Export All Comments</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Messages</h3>
                    <p>Export all group messages separately</p>
                    <div id="allMessagesProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allMessagesStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allMessages')">Export All Messages</button>
            </div>

            <button class="btn export-all-btn" onclick="exportAllAdminData()" style="background: #6f42c1;">Export ALL Collections</button>
        </div>
    `;

    // Insert admin section before the regular export section
    const firstCollectionItem = exportSection.querySelector('.collection-item');
    exportSection.insertBefore(adminSection, firstCollectionItem);
}

// Export functions
async function exportCollection(collectionType) {
    if (!currentUser) {
        alert('Please sign in first');
        return;
    }
    
    const progressEl = document.getElementById(`${collectionType}Progress`);
    const statusEl = document.getElementById(`${collectionType}Status`);
    const progressBar = progressEl.querySelector('.progress-bar');
    
    progressEl.style.display = 'block';
    statusEl.style.display = 'none';
    progressBar.style.width = '0%';
    
    try {
        let data = [];
        let filename = '';
        
        switch (collectionType) {
            case 'aiChats':
                data = await exportAIChats();
                filename = 'isotope-ai-chats.csv';
                break;
            case 'groups':
                data = await exportGroups();
                filename = 'isotope-groups-messages.csv';
                break;
            case 'todos':
                data = await exportTodos();
                filename = 'isotope-todos.csv';
                break;
            case 'subjects':
                data = await exportSubjects();
                filename = 'isotope-subjects.csv';
                break;
            case 'exams':
                data = await exportExams();
                filename = 'isotope-exams.csv';
                break;
            case 'userData':
                data = await exportUserData();
                filename = 'isotope-user-data.csv';
                break;
            // Admin-only exports
            case 'allUsers':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllUsers();
                filename = 'isotope-all-users.csv';
                break;
            case 'allAIChats':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllAIChats();
                filename = 'isotope-all-ai-chats.csv';
                break;
            case 'allGroups':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllGroups();
                filename = 'isotope-all-groups.csv';
                break;
            case 'allTodos':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllTodos();
                filename = 'isotope-all-todos.csv';
                break;
            case 'allSubjects':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllSubjects();
                filename = 'isotope-all-subjects.csv';
                break;
            case 'allExams':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllExams();
                filename = 'isotope-all-exams.csv';
                break;
            case 'allStudySessions':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllStudySessions();
                filename = 'isotope-all-study-sessions.csv';
                break;
            case 'allMockTests':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllMockTests();
                filename = 'isotope-all-mock-tests.csv';
                break;
            case 'allChatComments':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllChatComments();
                filename = 'isotope-all-chat-comments.csv';
                break;
            case 'allMessages':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllMessages();
                filename = 'isotope-all-messages.csv';
                break;
            case 'studySessionsTable':
                data = await exportStudySessionsTable();
                filename = 'isotope-study-sessions-table.csv';
                break;
            case 'mockTestsTable':
                data = await exportMockTestsTable();
                filename = 'isotope-mock-tests-table.csv';
                break;
        }
        
        progressBar.style.width = '100%';
        
        if (data.length > 0) {
            downloadCSV(data, filename);
            showStatus(statusEl, `Successfully exported ${data.length} records`, 'success');
        } else {
            showStatus(statusEl, 'No data found to export', 'info');
        }
        
    } catch (error) {
        console.error(`Export error for ${collectionType}:`, error);
        showStatus(statusEl, `Export failed: ${error.message}`, 'error');
    }
}

async function exportAIChats() {
    const chatsRef = db.collection('aiChats');
    const query = chatsRef.where('userId', '==', currentUser.uid);
    const snapshot = await query.get();

    const data = [];
    snapshot.forEach(doc => {
        const chatData = doc.data();
        data.push({
            id: doc.id,
            userId: chatData.userId,
            createdBy: chatData.userId,
            title: chatData.title || '',
            slug: chatData.slug || '',
            messages: JSON.stringify(chatData.messages || []),
            createdAt: formatTimestamp(chatData.createdAt),
            updatedAt: formatTimestamp(chatData.updatedAt),
            isPublic: chatData.isPublic || false,
            viewCount: chatData.viewCount || 0,
            preview: chatData.preview || '',
            comments: JSON.stringify(chatData.comments || []),
            isPinned: chatData.isPinned || false,
            isStarred: chatData.isStarred || false,
            status: chatData.status || 'approved',
            tags: JSON.stringify(chatData.tags || [])
        });
    });

    return data;
}

async function exportGroups() {
    const groupsRef = db.collection('groups');
    const query = groupsRef.where('members', 'array-contains', currentUser.uid);
    const snapshot = await query.get();
    
    const data = [];
    
    for (const doc of snapshot.docs) {
        const groupData = doc.data();
        
        // Get messages for this group
        const messagesRef = db.collection(`groups/${doc.id}/messages`);
        const messagesSnapshot = await messagesRef.orderBy('timestamp', 'asc').get();
        
        const messages = [];
        messagesSnapshot.forEach(msgDoc => {
            const msgData = msgDoc.data();
            messages.push({
                id: msgDoc.id,
                content: msgData.content,
                senderId: msgData.senderId,
                timestamp: formatTimestamp(msgData.timestamp)
            });
        });
        
        data.push({
            id: doc.id,
            name: groupData.name,
            description: groupData.description || '',
            members: JSON.stringify(groupData.members || []),
            createdBy: groupData.ownerId || groupData.createdBy,
            createdAt: formatTimestamp(groupData.createdAt),
            isPublic: groupData.isPublic || false,
            inviteCode: groupData.inviteCode || ''
        });
    }
    
    return data;
}

async function exportTodos() {
    const todosRef = db.collection('todos');
    const query = todosRef.where('createdBy', '==', currentUser.uid);
    const snapshot = await query.get();
    
    const data = [];
    snapshot.forEach(doc => {
        const todoData = doc.data();
        data.push({
            id: doc.id,
            title: todoData.title,
            description: todoData.description || '',
            priority: todoData.priority || 'medium',
            createdAt: todoData.createdAt,
            updatedAt: todoData.updatedAt,
            dueDate: todoData.dueDate || '',
            assignedTo: todoData.assignedTo || '',
            assignedToName: todoData.assignedToName || '',
            assignedToPhotoUrl: todoData.assignedToPhotoUrl || '',
            createdBy: todoData.createdBy,
            groupId: todoData.groupId || '',
            columnId: todoData.columnId || 'column-1'
        });
    });
    
    return data;
}

async function exportSubjects() {
    const subjectsRef = db.collection('userSubjects').doc(currentUser.uid);
    const doc = await subjectsRef.get();

    const data = [];
    if (doc.exists) { // Fixed: removed parentheses for v8 compat
        const subjectsData = doc.data();
        const subjects = subjectsData.subjects || [];

        subjects.forEach(subject => {
            data.push({
                id: subject.id,
                userId: currentUser.uid,
                name: subject.name,
                color: subject.color || '#000000',
                createdAt: formatTimestamp(subject.createdAt)
            });
        });
    }

    return data;
}

async function exportExams() {
    const examsRef = db.collection('exams');
    const query = examsRef.where('userId', '==', currentUser.uid);
    const snapshot = await query.get();
    
    const data = [];
    snapshot.forEach(doc => {
        const examData = doc.data();
        data.push({
            id: doc.id,
            userId: examData.userId,
            name: examData.name,
            date: examData.date,
            totalMarks: parseInt(examData.totalMarks) || 0,
            totalMarksObtained: parseInt(examData.totalMarksObtained) || 0,
            subjectMarks: JSON.stringify(examData.subjectMarks || {}),
            notes: examData.notes || '',
            createdAt: formatTimestamp(examData.createdAt)
        });
    });
    
    return data;
}

async function exportUserData() {
    const userRef = db.collection('users').doc(currentUser.uid);
    const doc = await userRef.get();

    const data = [];
    if (doc.exists) { // Fixed: removed parentheses for v8 compat
        const userData = doc.data();
        
        // Export study sessions
        const studySessions = userData.studySessions || {};
        Object.entries(studySessions).forEach(([sessionId, session]) => {
            data.push({
                type: 'study_session',
                id: sessionId,
                userId: currentUser.uid,
                subject: session.subject || '',
                duration: session.duration || 0,
                mode: session.mode || 'stopwatch',
                phase: session.phase || 'work',
                completed: session.completed || false,
                startTime: session.startTime,
                endTime: session.endTime || '',
                notes: session.notes || ''
            });
        });
        
        // Export mock tests
        const mockTests = userData.mockTests || {};
        Object.entries(mockTests).forEach(([testId, test]) => {
            data.push({
                type: 'mock_test',
                id: testId,
                userId: currentUser.uid,
                name: test.name,
                date: test.date,
                subjectMarks: JSON.stringify(test.subjectMarks || []),
                totalMarksObtained: parseInt(test.totalMarksObtained) || 0,
                totalMarks: parseInt(test.totalMarks) || 0,
                notes: test.notes || '',
                createdAt: test.createdAt
            });
        });
        
        // Export user profile
        data.push({
            type: 'user_profile',
            id: currentUser.uid,
            uid: currentUser.uid,
            email: userData.email || currentUser.email,
            displayName: userData.displayName || currentUser.displayName,
            photoURL: userData.photoURL || currentUser.photoURL,
            username: userData.username || '',
            created_at: formatTimestamp(userData.createdAt),
            lastLogin: formatTimestamp(userData.lastLogin),
            updated_at: formatTimestamp(userData.updatedAt || new Date()),
            welcomeEmailSent: userData.welcomeEmailSent || false,
            backgroundImage: userData.backgroundImage || '',
            bio: userData.bio || '',
            location: userData.location || '',
            stats: JSON.stringify(userData.stats || {}),
            progress: JSON.stringify(userData.progress || {}),
            studySessions: JSON.stringify(userData.studySessions || {}),
            mockTests: JSON.stringify(userData.mockTests || {})
        });
    }
    
    return data;
}

// Utility functions
function formatTimestamp(timestamp) {
    if (!timestamp) return '';
    if (timestamp.toDate) {
        return timestamp.toDate().toISOString();
    }
    if (typeof timestamp === 'number') {
        return new Date(timestamp).toISOString();
    }
    return timestamp;
}

function showStatus(statusEl, message, type) {
    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = 'block';
}

function downloadCSV(data, filename) {
    if (data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => 
            headers.map(header => {
                const value = row[header] || '';
                // Escape quotes and wrap in quotes if contains comma or quote
                if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                    return '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            }).join(',')
        )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

async function exportAllData() {
    const collections = ['aiChats', 'groups', 'todos', 'subjects', 'exams', 'userData'];

    for (const collection of collections) {
        await exportCollection(collection);
        // Add a small delay between exports
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    alert('All data exported successfully! Please check your downloads folder.');
}

async function exportAllAdminData() {
    if (!isAdmin()) {
        alert('Admin access required');
        return;
    }

    const adminCollections = [
        'allUsers', 'allAIChats', 'allGroups', 'allTodos',
        'allSubjects', 'allExams', 'allStudySessions',
        'allMockTests', 'allChatComments', 'allMessages'
    ];

    for (const collection of adminCollections) {
        await exportCollection(collection);
        // Add a small delay between exports
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    alert('All admin data exported successfully! Please check your downloads folder.');
}

// Admin export functions
async function exportAllUsers() {
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const userData = doc.data();
        data.push({
            id: doc.id,
            uid: doc.id,
            email: userData.email || '',
            displayName: userData.displayName || '',
            photoURL: userData.photoURL || '',
            username: userData.username || '',
            created_at: formatTimestamp(userData.createdAt),
            lastLogin: formatTimestamp(userData.lastLogin),
            updated_at: formatTimestamp(userData.updatedAt || new Date()),
            welcomeEmailSent: userData.welcomeEmailSent || false,
            backgroundImage: userData.backgroundImage || '',
            bio: userData.bio || '',
            location: userData.location || '',
            stats: JSON.stringify(userData.stats || {}),
            progress: JSON.stringify(userData.progress || {}),
            studySessions: JSON.stringify(userData.studySessions || {}),
            mockTests: JSON.stringify(userData.mockTests || {})
        });
    });

    return data;
}

async function exportAllAIChats() {
    const chatsRef = db.collection('aiChats');
    const snapshot = await chatsRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const chatData = doc.data();
        data.push({
            id: doc.id,
            userId: chatData.userId,
            createdBy: chatData.userId,
            title: chatData.title || '',
            slug: chatData.slug || '',
            messages: JSON.stringify(chatData.messages || []),
            createdAt: formatTimestamp(chatData.createdAt),
            updatedAt: formatTimestamp(chatData.updatedAt),
            isPublic: chatData.isPublic || false,
            viewCount: chatData.viewCount || 0,
            preview: chatData.preview || '',
            comments: JSON.stringify(chatData.comments || []),
            isPinned: chatData.isPinned || false,
            isStarred: chatData.isStarred || false,
            status: chatData.status || 'approved',
            tags: JSON.stringify(chatData.tags || [])
        });
    });

    return data;
}

async function exportAllGroups() {
    const groupsRef = db.collection('groups');
    const snapshot = await groupsRef.get();

    const data = [];

    for (const doc of snapshot.docs) {
        const groupData = doc.data();

        // Get messages for this group
        try {
            const messagesRef = db.collection(`groups/${doc.id}/messages`);
            const messagesSnapshot = await messagesRef.orderBy('timestamp', 'asc').get();

            const messages = [];
            messagesSnapshot.forEach(msgDoc => {
                const msgData = msgDoc.data();
                messages.push({
                    id: msgDoc.id,
                    content: msgData.content,
                    senderId: msgData.senderId,
                    timestamp: formatTimestamp(msgData.timestamp)
                });
            });

            data.push({
                id: doc.id,
                name: groupData.name,
                description: groupData.description || '',
                members: JSON.stringify(groupData.members || []),
                createdBy: groupData.ownerId || groupData.createdBy,
                createdAt: formatTimestamp(groupData.createdAt),
                isPublic: groupData.isPublic || false,
                inviteCode: groupData.inviteCode || ''
            });
        } catch (error) {
            console.warn(`Could not fetch messages for group ${doc.id}:`, error);
            data.push({
                id: doc.id,
                name: groupData.name,
                description: groupData.description || '',
                members: JSON.stringify(groupData.members || []),
                createdBy: groupData.ownerId || groupData.createdBy,
                createdAt: formatTimestamp(groupData.createdAt),
                isPublic: groupData.isPublic || false,
                inviteCode: groupData.inviteCode || ''
            });
        }
    }

    return data;
}

async function exportAllTodos() {
    const todosRef = db.collection('todos');
    const snapshot = await todosRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const todoData = doc.data();
        data.push({
            id: doc.id,
            title: todoData.title,
            description: todoData.description || '',
            priority: todoData.priority || 'medium',
            createdAt: todoData.createdAt,
            updatedAt: todoData.updatedAt,
            dueDate: todoData.dueDate || '',
            assignedTo: todoData.assignedTo || '',
            assignedToName: todoData.assignedToName || '',
            assignedToPhotoUrl: todoData.assignedToPhotoUrl || '',
            createdBy: todoData.createdBy,
            groupId: todoData.groupId || '',
            columnId: todoData.columnId || 'column-1'
        });
    });

    return data;
}

async function exportAllSubjects() {
    const usersRef = db.collection('userSubjects');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const subjectsData = doc.data();
        const subjects = subjectsData.subjects || [];

        subjects.forEach(subject => {
            data.push({
                id: subject.id,
                userId: doc.id,
                name: subject.name,
                color: subject.color || '#000000',
                createdAt: formatTimestamp(subject.createdAt)
            });
        });
    });

    return data;
}

async function exportAllExams() {
    const examsRef = db.collection('exams');
    const snapshot = await examsRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const examData = doc.data();
        data.push({
            id: doc.id,
            userId: examData.userId,
            name: examData.name,
            date: examData.date,
            totalMarks: parseInt(examData.totalMarks) || 0,
            totalMarksObtained: parseInt(examData.totalMarksObtained) || 0,
            subjectMarks: JSON.stringify(examData.subjectMarks || {}),
            notes: examData.notes || '',
            createdAt: formatTimestamp(examData.createdAt)
        });
    });

    return data;
}

async function exportAllStudySessions() {
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const userData = doc.data();
        const studySessions = userData.studySessions || {};

        Object.entries(studySessions).forEach(([sessionId, session]) => {
            data.push({
                id: sessionId,
                userId: doc.id,
                subject: session.subject || '',
                duration: session.duration || 0,
                mode: session.mode || 'stopwatch',
                phase: session.phase || 'work',
                completed: session.completed || false,
                startTime: session.startTime,
                endTime: session.endTime || '',
                notes: session.notes || ''
            });
        });
    });

    return data;
}

async function exportAllMockTests() {
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const userData = doc.data();
        const mockTests = userData.mockTests || {};

        Object.entries(mockTests).forEach(([testId, test]) => {
            data.push({
                id: testId,
                userId: doc.id,
                name: test.name,
                date: test.date,
                subjectMarks: JSON.stringify(test.subjectMarks || []),
                totalMarksObtained: parseInt(test.totalMarksObtained) || 0,
                totalMarks: parseInt(test.totalMarks) || 0,
                notes: test.notes || '',
                createdAt: test.createdAt
            });
        });
    });

    return data;
}

async function exportAllChatComments() {
    const chatsRef = db.collection('aiChats');
    const snapshot = await chatsRef.get();

    const data = [];

    for (const doc of snapshot.docs) {
        const chatData = doc.data();
        const comments = chatData.comments || [];

        comments.forEach(comment => {
            data.push({
                id: comment.id,
                chatId: doc.id,
                content: comment.content,
                author: comment.author,
                authorId: comment.authorId,
                authorUsername: comment.authorUsername,
                authorPhotoURL: comment.authorPhotoURL,
                timestamp: new Date(comment.timestamp).toISOString(),
                replies: JSON.stringify(comment.replies || []),
                mentionedUserIds: JSON.stringify(comment.mentionedUserIds || [])
            });
        });
    }

    return data;
}

async function exportAllMessages() {
    const groupsRef = db.collection('groups');
    const groupsSnapshot = await groupsRef.get();

    const data = [];

    for (const groupDoc of groupsSnapshot.docs) {
        try {
            const messagesRef = db.collection(`groups/${groupDoc.id}/messages`);
            const messagesSnapshot = await messagesRef.orderBy('timestamp', 'asc').get();

            messagesSnapshot.forEach(msgDoc => {
                const msgData = msgDoc.data();
                data.push({
                    id: msgDoc.id,
                    groupId: groupDoc.id,
                    senderId: msgData.senderId,
                    senderName: msgData.senderName || '',
                    content: msgData.content,
                    messageType: msgData.messageType || 'text',
                    metadata: JSON.stringify(msgData.metadata || {}),
                    createdAt: formatTimestamp(msgData.timestamp),
                    updatedAt: formatTimestamp(msgData.timestamp)
                });
            });
        } catch (error) {
            console.warn(`Could not fetch messages for group ${groupDoc.id}:`, error);
        }
    }

    return data;
}

// Export study sessions as separate table records
async function exportStudySessionsTable() {
    const userRef = db.collection('users').doc(currentUser.uid);
    const doc = await userRef.get();

    const data = [];
    if (doc.exists) {
        const userData = doc.data();
        const studySessions = userData.studySessions || {};

        Object.entries(studySessions).forEach(([sessionId, session]) => {
            data.push({
                id: sessionId,
                user_id: currentUser.uid,
                subject: session.subject || '',
                duration: parseInt(session.duration) || 0,
                mode: session.mode || 'stopwatch',
                phase: session.phase || 'work',
                completed: session.completed || false,
                start_time: formatTimestamp(session.startTime),
                end_time: formatTimestamp(session.endTime),
                notes: session.notes || '',
                created_at: formatTimestamp(session.startTime)
            });
        });
    }

    return data;
}

// Export mock tests as separate table records
async function exportMockTestsTable() {
    const userRef = db.collection('users').doc(currentUser.uid);
    const doc = await userRef.get();

    const data = [];
    if (doc.exists) {
        const userData = doc.data();
        const mockTests = userData.mockTests || {};

        Object.entries(mockTests).forEach(([testId, test]) => {
            data.push({
                id: testId,
                user_id: currentUser.uid,
                name: test.name,
                test_date: test.date,
                subject_marks: JSON.stringify(test.subjectMarks || []),
                total_marks_obtained: parseInt(test.totalMarksObtained) || 0,
                total_marks: parseInt(test.totalMarks) || 0,
                notes: test.notes || '',
                created_at: formatTimestamp(test.createdAt),
                updated_at: formatTimestamp(test.createdAt)
            });
        });
    }

    return data;
}
